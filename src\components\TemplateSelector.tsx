'use client';

import { useOrderStore } from '@/store/orderStore';
import { ReceiptTemplate } from '@/types/order';
import { Check, Palette } from 'lucide-react';

export function TemplateSelector() {
  const { templates, selectedTemplate, setSelectedTemplate } = useOrderStore();

  const handleTemplateSelect = (template: ReceiptTemplate) => {
    setSelectedTemplate(template);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center mb-4">
        <Palette className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">选择模板</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {templates.map((template) => (
          <div
            key={template.id}
            className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
              selectedTemplate?.id === template.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleTemplateSelect(template)}
          >
            {/* 选中状态指示器 */}
            {selectedTemplate?.id === template.id && (
              <div className="absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                <Check className="w-4 h-4 text-white" />
              </div>
            )}

            {/* 模板预览 */}
            <div className="mb-3">
              <div
                className="w-full h-32 rounded-md border border-gray-200 flex items-center justify-center text-gray-400 text-sm"
                style={{
                  backgroundColor: template.styles.secondaryColor,
                  color: template.styles.primaryColor
                }}
              >
                <div className="text-center">
                  <div className="font-semibold mb-1">{template.name}</div>
                  <div className="text-xs">预览</div>
                </div>
              </div>
            </div>

            {/* 模板信息 */}
            <div>
              <h4 className="font-medium text-gray-900 mb-1">{template.name}</h4>
              <p className="text-sm text-gray-600 mb-3">{template.description}</p>

              {/* 样式信息 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">主色调:</span>
                  <div
                    className="w-4 h-4 rounded border border-gray-300"
                    style={{ backgroundColor: template.styles.primaryColor }}
                  />
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">布局:</span>
                  <span className="text-gray-700 capitalize">{template.styles.layout}</span>
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">字体:</span>
                  <span className="text-gray-700">{template.styles.fontFamily.split(',')[0]}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 自定义模板提示 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Palette className="w-5 h-5 text-gray-400 mt-0.5" />
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-gray-900">自定义模板</h4>
            <p className="text-sm text-gray-600 mt-1">
              需要更多模板样式？您可以联系我们定制专属的收据模板，满足您的品牌需求。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
