import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthStore, LoginCredentials, RegisterCredentials, User } from '@/types/auth';
import { authService } from '@/services/authService';

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const user = await authService.login(credentials);
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
          
          // 登录成功后同步本地数据到云端
          await get().syncLocalDataToCloud();
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '登录失败',
            isLoading: false 
          });
          throw error;
        }
      },

      register: async (credentials: RegisterCredentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const user = await authService.register(credentials);
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
          
          // 注册成功后同步本地数据到云端
          await get().syncLocalDataToCloud();
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '注册失败',
            isLoading: false 
          });
          throw error;
        }
      },

      logout: () => {
        authService.logout();
        set({ 
          user: null, 
          isAuthenticated: false, 
          error: null 
        });
      },

      clearError: () => {
        set({ error: null });
      },

      checkAuth: async () => {
        set({ isLoading: true });
        
        try {
          const user = await authService.getCurrentUser();
          if (user) {
            set({ 
              user, 
              isAuthenticated: true, 
              isLoading: false 
            });
          } else {
            set({ 
              user: null, 
              isAuthenticated: false, 
              isLoading: false 
            });
          }
        } catch (error) {
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: error instanceof Error ? error.message : '认证检查失败'
          });
        }
      },

      updateProfile: async (updates: Partial<User>) => {
        const { user } = get();
        if (!user) return;

        set({ isLoading: true, error: null });
        
        try {
          const updatedUser = await authService.updateProfile(updates);
          set({ 
            user: updatedUser, 
            isLoading: false 
          });
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : '更新失败',
            isLoading: false 
          });
          throw error;
        }
      },

      // 同步本地数据到云端
      syncLocalDataToCloud: async () => {
        const { user } = get();
        if (!user) return;

        try {
          // 获取本地订单数据
          const localOrders = JSON.parse(localStorage.getItem('order-store') || '{}');
          
          if (localOrders.state?.orders?.length > 0) {
            // 同步到云端
            await authService.syncOrdersToCloud(localOrders.state.orders);
            
            // 清除本地数据（可选，或者保留作为缓存）
            // localStorage.removeItem('order-store');
          }
        } catch (error) {
          console.error('同步数据到云端失败:', error);
        }
      }
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
