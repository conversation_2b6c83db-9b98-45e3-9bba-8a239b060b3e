@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #ffffff;
    --foreground: #0f172a;
  }

  .dark {
    --background: #0f172a;
    --foreground: #f8fafc;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 输入框样式优化 */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="number"],
  input[type="date"],
  textarea,
  select {
    @apply text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800;
  }

  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="tel"]:focus,
  input[type="number"]:focus,
  input[type="date"]:focus,
  textarea:focus,
  select:focus {
    @apply ring-2 ring-blue-500 dark:ring-blue-400;
  }

  /* 占位符文本 */
  ::placeholder {
    @apply text-gray-400 dark:text-gray-500;
  }
}
