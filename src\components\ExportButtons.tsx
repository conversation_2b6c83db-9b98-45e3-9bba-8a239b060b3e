'use client';

import { useState } from 'react';
import { useOrderStore } from '@/store/orderStore';
import { useBusinessStore } from '@/store/businessStore';
import { ExportOptions } from '@/types/order';
import { Download, FileText, Image, Save, Settings } from 'lucide-react';
import { exportToPDF, exportToPNG } from '@/utils/exportUtils';

export function ExportButtons() {
  const { currentOrder, saveOrder } = useOrderStore();
  const { businessInfo } = useBusinessStore();
  const [isExporting, setIsExporting] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    quality: 1,
    size: 'a4',
    orientation: 'portrait'
  });

  const handleSaveOrder = () => {
    if (currentOrder) {
      saveOrder(currentOrder);
      // 可以添加成功提示
      alert('订单已保存！');
    }
  };

  const handleExport = async (format: 'pdf' | 'png') => {
    if (!currentOrder) {
      alert('请先填写订单信息');
      return;
    }

    setIsExporting(true);

    try {
      const options = { ...exportOptions, format };

      if (format === 'pdf') {
        await exportToPDF(currentOrder, options, businessInfo);
      } else {
        await exportToPNG(currentOrder, options, businessInfo);
      }

      // 导出成功后自动保存订单
      saveOrder(currentOrder);
    } catch (error) {
      console.error('Export failed:', error);
      alert('导出失败，请重试');
    } finally {
      setIsExporting(false);
    }
  };

  const isOrderValid = currentOrder &&
    currentOrder.customer.name &&
    currentOrder.customer.phone &&
    currentOrder.address.shippingAddress &&
    currentOrder.orderInfo.items.length > 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center mb-4">
        <Download className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">导出与保存</h3>
      </div>

      {/* 导出选项 */}
      {showOptions && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 mb-3">导出设置</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                纸张大小
              </label>
              <select
                value={exportOptions.size}
                onChange={(e) => setExportOptions(prev => ({
                  ...prev,
                  size: e.target.value as 'a4' | 'letter' | 'custom'
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="a4">A4</option>
                <option value="letter">Letter</option>
                <option value="custom">自定义</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                方向
              </label>
              <select
                value={exportOptions.orientation}
                onChange={(e) => setExportOptions(prev => ({
                  ...prev,
                  orientation: e.target.value as 'portrait' | 'landscape'
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="portrait">纵向</option>
                <option value="landscape">横向</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                图片质量 (PNG)
              </label>
              <select
                value={exportOptions.quality}
                onChange={(e) => setExportOptions(prev => ({
                  ...prev,
                  quality: Number(e.target.value)
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={0.8}>标准 (80%)</option>
                <option value={1}>高质量 (100%)</option>
                <option value={1.5}>超高质量 (150%)</option>
                <option value={2}>打印质量 (200%)</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="space-y-3">
        {/* 保存订单 */}
        <button
          onClick={handleSaveOrder}
          disabled={!isOrderValid}
          className={`w-full flex items-center justify-center px-4 py-3 rounded-md font-medium transition-colors ${
            isOrderValid
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <Save className="w-5 h-5 mr-2" />
          保存订单
        </button>

        {/* 导出按钮组 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <button
            onClick={() => handleExport('pdf')}
            disabled={!isOrderValid || isExporting}
            className={`flex items-center justify-center px-4 py-3 rounded-md font-medium transition-colors ${
              isOrderValid && !isExporting
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <FileText className="w-5 h-5 mr-2" />
            {isExporting && exportOptions.format === 'pdf' ? '导出中...' : '导出 PDF'}
          </button>

          <button
            onClick={() => handleExport('png')}
            disabled={!isOrderValid || isExporting}
            className={`flex items-center justify-center px-4 py-3 rounded-md font-medium transition-colors ${
              isOrderValid && !isExporting
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <Image className="w-5 h-5 mr-2" />
            {isExporting && exportOptions.format === 'png' ? '导出中...' : '导出 PNG'}
          </button>
        </div>

        {/* 设置按钮 */}
        <button
          onClick={() => setShowOptions(!showOptions)}
          className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          <Settings className="w-4 h-4 mr-2" />
          {showOptions ? '隐藏设置' : '导出设置'}
        </button>
      </div>

      {/* 提示信息 */}
      {!isOrderValid && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            请完善以下信息后再导出：
          </p>
          <ul className="text-sm text-yellow-700 mt-1 ml-4 list-disc">
            {!currentOrder?.customer.name && <li>客户姓名</li>}
            {!currentOrder?.customer.phone && <li>联系电话</li>}
            {!currentOrder?.address.shippingAddress && <li>收货地址</li>}
            {(!currentOrder?.orderInfo.items || currentOrder.orderInfo.items.length === 0) && <li>至少一个商品</li>}
          </ul>
        </div>
      )}
    </div>
  );
}
