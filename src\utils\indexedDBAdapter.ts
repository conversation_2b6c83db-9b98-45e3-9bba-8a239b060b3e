import { StateStorage } from 'zustand/middleware';
import { dbManager } from './indexedDB';

// IndexedDB 存储适配器
export class IndexedDBAdapter implements StateStorage {
  private storeName: string;

  constructor(storeName: string) {
    this.storeName = storeName;
  }

  async getItem(name: string): Promise<string | null> {
    try {
      await dbManager.init();
      const data = await dbManager.get('settings', `${this.storeName}_${name}`);
      return data ? JSON.stringify(data.value) : null;
    } catch (error) {
      console.error(`Failed to get item ${name}:`, error);
      return null;
    }
  }

  async setItem(name: string, value: string): Promise<void> {
    try {
      await dbManager.init();
      await dbManager.put('settings', {
        key: `${this.storeName}_${name}`,
        value: JSON.parse(value),
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Failed to set item ${name}:`, error);
    }
  }

  async removeItem(name: string): Promise<void> {
    try {
      await dbManager.init();
      await dbManager.delete('settings', `${this.storeName}_${name}`);
    } catch (error) {
      console.error(`Failed to remove item ${name}:`, error);
    }
  }
}

// 创建专用的数据存储类
export class OrderDataStore {
  async init(): Promise<void> {
    await dbManager.init();
  }

  // 订单相关方法
  async saveOrder(order: any): Promise<void> {
    await dbManager.put('orders', order);
  }

  async getOrder(id: string): Promise<any> {
    return await dbManager.get('orders', id);
  }

  async getAllOrders(): Promise<any[]> {
    return await dbManager.getAll('orders');
  }

  async deleteOrder(id: string): Promise<void> {
    await dbManager.delete('orders', id);
  }

  async getOrdersByCustomer(customerName: string): Promise<any[]> {
    return await dbManager.getByIndex('orders', 'by_customer', customerName);
  }

  // 商品相关方法
  async saveProduct(product: any): Promise<void> {
    await dbManager.put('products', product);
  }

  async getProduct(id: string): Promise<any> {
    return await dbManager.get('products', id);
  }

  async getAllProducts(): Promise<any[]> {
    return await dbManager.getAll('products');
  }

  async deleteProduct(id: string): Promise<void> {
    await dbManager.delete('products', id);
  }

  async getProductsByCategory(category: string): Promise<any[]> {
    return await dbManager.getByIndex('products', 'by_category', category);
  }

  // 商业信息相关方法
  async saveBusiness(business: any): Promise<void> {
    await dbManager.put('business', business);
  }

  async getBusiness(id: string): Promise<any> {
    return await dbManager.get('business', id);
  }

  async getAllBusiness(): Promise<any[]> {
    return await dbManager.getAll('business');
  }

  // 用户相关方法
  async saveUser(user: any): Promise<void> {
    await dbManager.put('users', user);
  }

  async getUser(id: string): Promise<any> {
    return await dbManager.get('users', id);
  }

  async getUserByEmail(email: string): Promise<any> {
    const users = await dbManager.getByIndex('users', 'by_email', email);
    return users[0] || null;
  }

  async getAllUsers(): Promise<any[]> {
    return await dbManager.getAll('users');
  }

  async deleteUser(id: string): Promise<void> {
    await dbManager.delete('users', id);
  }

  // 设置相关方法
  async saveSetting(key: string, value: any): Promise<void> {
    await dbManager.put('settings', {
      key,
      value,
      timestamp: new Date().toISOString()
    });
  }

  async getSetting(key: string): Promise<any> {
    const setting = await dbManager.get('settings', key);
    return setting?.value || null;
  }

  async deleteSetting(key: string): Promise<void> {
    await dbManager.delete('settings', key);
  }

  // 统计方法
  async getOrderCount(): Promise<number> {
    return await dbManager.count('orders');
  }

  async getProductCount(): Promise<number> {
    return await dbManager.count('products');
  }

  async getUserCount(): Promise<number> {
    return await dbManager.count('users');
  }

  // 清理方法
  async clearAllOrders(): Promise<void> {
    await dbManager.clear('orders');
  }

  async clearAllProducts(): Promise<void> {
    await dbManager.clear('products');
  }

  async clearAllUsers(): Promise<void> {
    await dbManager.clear('users');
  }

  async clearAllSettings(): Promise<void> {
    await dbManager.clear('settings');
  }
}

// 创建全局数据存储实例
export const dataStore = new OrderDataStore();
