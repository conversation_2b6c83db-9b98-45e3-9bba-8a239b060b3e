'use client';

import { useOrderStore } from '@/store/orderStore';
import { formatCurrency } from '@/utils/orderUtils';
import { BarChart3, TrendingUp, DollarSign, ShoppingCart, Calendar } from 'lucide-react';
import { useMemo } from 'react';

export default function AnalyticsPage() {
  const { orders } = useOrderStore();

  // 计算统计数据
  const stats = useMemo(() => {
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.orderInfo.total, 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // 本月订单
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthlyOrders = orders.filter(order => {
      const orderDate = new Date(order.orderInfo.date);
      return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
    });

    const monthlyRevenue = monthlyOrders.reduce((sum, order) => sum + order.orderInfo.total, 0);

    // 最受欢迎的商品
    const itemCounts: Record<string, number> = {};
    orders.forEach(order => {
      order.orderInfo.items.forEach(item => {
        itemCounts[item.name] = (itemCounts[item.name] || 0) + item.quantity;
      });
    });

    const topItems = Object.entries(itemCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);

    return {
      totalOrders,
      totalRevenue,
      averageOrderValue,
      monthlyOrders: monthlyOrders.length,
      monthlyRevenue,
      topItems
    };
  }, [orders]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">统计分析</h1>
          <p className="mt-2 text-lg text-gray-600">
            查看您的订单数据和业务洞察
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ShoppingCart className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总订单数</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.totalOrders}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总收入</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{formatCurrency(stats.totalRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">平均订单价值</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{formatCurrency(stats.averageOrderValue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">本月订单</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{stats.monthlyOrders}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 本月收入 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <BarChart3 className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">本月收入</h3>
            </div>
            <div className="text-center py-8">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {formatCurrency(stats.monthlyRevenue)}
              </div>
              <p className="text-gray-600">
                来自 {stats.monthlyOrders} 个订单
              </p>
            </div>
          </div>

          {/* 热销商品 */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center mb-4">
              <TrendingUp className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">热销商品 TOP 5</h3>
            </div>

            {stats.topItems.length > 0 ? (
              <div className="space-y-3">
                {stats.topItems.map(([itemName, count], index) => (
                  <div key={itemName} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mr-3">
                        {index + 1}
                      </span>
                      <span className="text-gray-900">{itemName}</span>
                    </div>
                    <span className="text-gray-600 font-medium">{count} 件</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                暂无数据
              </div>
            )}
          </div>
        </div>

        {/* 功能预告 */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <div className="text-center">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">更多功能即将推出</h3>
            <p className="mt-1 text-sm text-gray-500">
              我们正在开发更多强大的统计分析功能，包括：
            </p>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
              <div className="flex items-center justify-center p-3 bg-gray-50 rounded-md">
                📊 销售趋势图表
              </div>
              <div className="flex items-center justify-center p-3 bg-gray-50 rounded-md">
                📈 收入增长分析
              </div>
              <div className="flex items-center justify-center p-3 bg-gray-50 rounded-md">
                👥 客户分析报告
              </div>
              <div className="flex items-center justify-center p-3 bg-gray-50 rounded-md">
                📅 周报/月报生成
              </div>
              <div className="flex items-center justify-center p-3 bg-gray-50 rounded-md">
                🎯 销售目标跟踪
              </div>
              <div className="flex items-center justify-center p-3 bg-gray-50 rounded-md">
                📋 库存管理建议
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
