# 订单收据开单工具

一个基于 Next.js 构建的现代化订单收据生成工具，支持多种模板样式和导出格式，完美适配 PC 和移动端设备。

## ✨ 功能特性

### 核心功能
- 📝 **订单信息管理** - 完整的客户信息、地址信息和订单详情录入
- 🎨 **多种模板样式** - 现代风格、经典风格、极简风格三种预设模板
- 📄 **多格式导出** - 支持 PDF 和 PNG 格式导出
- 📱 **响应式设计** - 完美适配 PC、平板和手机设备
- 💾 **本地存储** - 自动保存订单数据到本地存储
- 🔍 **历史订单管理** - 查看、编辑、删除历史订单记录
- 📊 **统计分析** - 基础的销售数据统计和分析
- 🌙 **明/暗主题** - 支持浅色、深色和跟随系统三种主题模式
- 👤 **用户系统** - 注册/登录功能，支持云端数据同步
- ☁️ **云端同步** - 登录后自动将订单数据同步到云端

### 技术特性
- ⚡ **Next.js 15** - 使用最新的 App Router
- 🎯 **TypeScript** - 完整的类型安全
- 🎨 **Tailwind CSS** - 现代化的样式框架，支持暗色模式
- 🗃️ **Zustand** - 轻量级状态管理
- 📦 **组件化设计** - 高度模块化的组件架构
- 🔐 **本地认证** - 模拟的用户认证系统
- 🔄 **数据同步** - 本地与云端数据自动同步

## 🚀 快速开始

### 环境要求
- Node.js 18.0 或更高版本
- npm、yarn、pnpm 或 bun

### 安装依赖
```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本
```bash
npm run build
npm start
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── page.tsx           # 首页 - 订单创建
│   ├── history/           # 历史订单页面
│   ├── analytics/         # 统计分析页面
│   └── layout.tsx         # 根布局
├── components/            # React 组件
│   ├── OrderForm.tsx      # 订单表单组件
│   ├── TemplateSelector.tsx # 模板选择器
│   ├── ExportButtons.tsx  # 导出按钮组件
│   ├── ReceiptPreview.tsx # 收据预览组件
│   └── Navigation.tsx     # 导航组件
├── store/                 # 状态管理
│   └── orderStore.ts      # 订单状态管理
├── types/                 # TypeScript 类型定义
│   └── order.ts           # 订单相关类型
└── utils/                 # 工具函数
    ├── orderUtils.ts      # 订单工具函数
    └── exportUtils.ts     # 导出工具函数
```

## 🎯 使用指南

### 1. 主题切换
- 点击导航栏右侧的主题切换按钮
- 支持三种模式：浅色、深色、跟随系统
- 设置会自动保存到本地存储

### 2. 用户注册/登录
1. 点击导航栏的"登录"按钮
2. 选择"注册"创建新账户或"登录"现有账户
3. 登录后可享受云端数据同步功能
4. 未登录状态下，数据仅保存在本地

### 3. 创建订单
1. 填写客户信息（姓名、电话、邮箱）
2. 输入收货地址和账单地址
3. 添加商品信息（名称、数量、单价）
4. 设置税费和折扣（可选）
5. 添加订单备注（可选）

### 4. 选择模板
- **现代风格** - 简洁现代的设计，适合大多数业务场景
- **经典风格** - 传统商务风格，正式专业
- **极简风格** - 极简设计，突出重要信息

### 5. 导出收据
- **PDF 格式** - 适合打印和正式文档
- **PNG 格式** - 适合在线分享和展示
- 支持多种纸张大小和方向设置

### 6. 管理历史订单
- 查看所有历史订单
- 搜索和筛选订单
- 编辑现有订单
- 重新导出订单收据

### 7. 云端同步
- 登录后在用户菜单中可开启/关闭云端同步
- 开启后订单数据会自动同步到云端
- 支持多设备数据同步

## 🔧 自定义配置

### 添加新模板
在 `src/store/orderStore.ts` 中的 `defaultTemplates` 数组中添加新模板：

```typescript
{
  id: 'custom',
  name: '自定义模板',
  description: '您的自定义模板描述',
  preview: '/templates/custom-preview.png',
  styles: {
    primaryColor: '#your-color',
    secondaryColor: '#your-secondary-color',
    fontFamily: 'Your Font Family',
    fontSize: '14px',
    layout: 'modern'
  }
}
```

### 修改导出设置
在 `src/utils/exportUtils.ts` 中自定义 PDF 和 PNG 导出逻辑。

## 📱 移动端支持

应用完全支持移动端设备：
- 响应式布局自动适配屏幕尺寸
- 触摸友好的交互设计
- 移动端优化的表单输入
- 支持移动端浏览器的所有功能

## 🔮 未来计划

- [ ] 云端数据同步
- [ ] 更多模板样式
- [ ] 批量导出功能
- [ ] 客户管理系统
- [ ] 库存管理集成
- [ ] 多语言支持
- [ ] PWA 支持
- [ ] 打印机直连功能

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进这个项目。

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Zustand](https://github.com/pmndrs/zustand) - 状态管理
- [jsPDF](https://github.com/parallax/jsPDF) - PDF 生成
- [html2canvas](https://github.com/niklasvh/html2canvas) - 截图功能
- [Lucide React](https://lucide.dev/) - 图标库
