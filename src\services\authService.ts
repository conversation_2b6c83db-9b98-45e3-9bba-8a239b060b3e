import { LoginCredentials, RegisterCredentials, User } from '@/types/auth';
import { Order } from '@/types/order';

// 模拟 API 延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟用户数据库
const USERS_KEY = 'app_users';
const TOKENS_KEY = 'app_tokens';

interface StoredUser extends User {
  password: string;
}

class AuthService {
  private getUsers(): StoredUser[] {
    const users = localStorage.getItem(USERS_KEY);
    return users ? JSON.parse(users) : [];
  }

  private saveUsers(users: StoredUser[]): void {
    localStorage.setItem(USERS_KEY, JSON.stringify(users));
  }

  private generateToken(): string {
    return Math.random().toString(36).substr(2) + Date.now().toString(36);
  }

  private generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private setToken(token: string): void {
    localStorage.setItem(TOKENS_KEY, token);
  }

  private getToken(): string | null {
    return localStorage.getItem(TOKENS_KEY);
  }

  private removeToken(): void {
    localStorage.removeItem(TOKENS_KEY);
  }

  async login(credentials: LoginCredentials): Promise<User> {
    await delay(1000); // 模拟网络延迟

    const users = this.getUsers();
    const user = users.find(u => u.email === credentials.email);

    if (!user) {
      throw new Error('用户不存在');
    }

    if (user.password !== credentials.password) {
      throw new Error('密码错误');
    }

    const token = this.generateToken();
    this.setToken(token);

    // 返回用户信息（不包含密码）
    const { password, ...userInfo } = user;
    return userInfo;
  }

  async register(credentials: RegisterCredentials): Promise<User> {
    await delay(1000); // 模拟网络延迟

    if (credentials.password !== credentials.confirmPassword) {
      throw new Error('两次输入的密码不一致');
    }

    if (credentials.password.length < 6) {
      throw new Error('密码长度至少为6位');
    }

    const users = this.getUsers();
    
    // 检查邮箱是否已存在
    if (users.some(u => u.email === credentials.email)) {
      throw new Error('该邮箱已被注册');
    }

    const newUser: StoredUser = {
      id: this.generateUserId(),
      name: credentials.name,
      email: credentials.email,
      password: credentials.password,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    users.push(newUser);
    this.saveUsers(users);

    const token = this.generateToken();
    this.setToken(token);

    // 返回用户信息（不包含密码）
    const { password, ...userInfo } = newUser;
    return userInfo;
  }

  async getCurrentUser(): Promise<User | null> {
    await delay(500); // 模拟网络延迟

    const token = this.getToken();
    if (!token) {
      return null;
    }

    // 在真实应用中，这里会验证 token 的有效性
    // 现在我们简单地检查是否有 token
    const users = this.getUsers();
    if (users.length === 0) {
      return null;
    }

    // 模拟：返回最后登录的用户（在真实应用中应该通过 token 解析用户）
    const lastUser = users[users.length - 1];
    const { password, ...userInfo } = lastUser;
    return userInfo;
  }

  async updateProfile(updates: Partial<User>): Promise<User> {
    await delay(800); // 模拟网络延迟

    const token = this.getToken();
    if (!token) {
      throw new Error('未登录');
    }

    const users = this.getUsers();
    const currentUser = await this.getCurrentUser();
    
    if (!currentUser) {
      throw new Error('用户不存在');
    }

    const userIndex = users.findIndex(u => u.id === currentUser.id);
    if (userIndex === -1) {
      throw new Error('用户不存在');
    }

    // 更新用户信息
    const updatedUser = {
      ...users[userIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    users[userIndex] = updatedUser;
    this.saveUsers(users);

    const { password, ...userInfo } = updatedUser;
    return userInfo;
  }

  logout(): void {
    this.removeToken();
  }

  // 同步订单到云端（模拟）
  async syncOrdersToCloud(orders: Order[]): Promise<void> {
    await delay(1500); // 模拟网络延迟

    const currentUser = await this.getCurrentUser();
    if (!currentUser) {
      throw new Error('未登录');
    }

    // 在真实应用中，这里会将订单数据发送到服务器
    // 现在我们将其存储在 localStorage 中，使用用户 ID 作为键
    const userOrdersKey = `user_orders_${currentUser.id}`;
    const existingOrders = JSON.parse(localStorage.getItem(userOrdersKey) || '[]');
    
    // 合并订单（避免重复）
    const mergedOrders = [...existingOrders];
    
    orders.forEach(newOrder => {
      const existingIndex = mergedOrders.findIndex(order => order.id === newOrder.id);
      if (existingIndex >= 0) {
        // 更新现有订单
        mergedOrders[existingIndex] = newOrder;
      } else {
        // 添加新订单
        mergedOrders.push(newOrder);
      }
    });

    localStorage.setItem(userOrdersKey, JSON.stringify(mergedOrders));
    
    console.log(`已同步 ${orders.length} 个订单到云端`);
  }

  // 从云端获取订单（模拟）
  async getOrdersFromCloud(): Promise<Order[]> {
    await delay(1000); // 模拟网络延迟

    const currentUser = await this.getCurrentUser();
    if (!currentUser) {
      throw new Error('未登录');
    }

    const userOrdersKey = `user_orders_${currentUser.id}`;
    const orders = JSON.parse(localStorage.getItem(userOrdersKey) || '[]');
    
    return orders;
  }
}

export const authService = new AuthService();
