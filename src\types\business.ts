export interface BusinessInfo {
  id: string;
  companyName: string;
  phone: string;
  wechat?: string;
  address: string;
  qrCode?: string; // base64 编码的二维码图片
  logo?: string; // base64 编码的 logo 图片
  email?: string;
  website?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BusinessStore {
  businessInfo: BusinessInfo | null;

  // Actions
  updateBusinessInfo: (info: Partial<BusinessInfo>) => Promise<void>;
  setBusinessInfo: (info: BusinessInfo) => Promise<void>;
  clearBusinessInfo: () => void;
  loadBusinessInfo: () => Promise<void>;
  uploadQRCode: (file: File) => Promise<string>;
  uploadLogo: (file: File) => Promise<string>;
}

// 默认商业信息
export const DEFAULT_BUSINESS_INFO: Omit<BusinessInfo, 'id' | 'createdAt' | 'updatedAt'> = {
  companyName: '您的企业名称',
  phone: '',
  wechat: '',
  address: '',
  qrCode: '',
  logo: '',
  email: '',
  website: '',
  description: ''
};
