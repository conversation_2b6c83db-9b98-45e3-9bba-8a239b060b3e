import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProductTemplate, ProductStore, ProductDimensions, CalculatedProduct } from '@/types/product';
import { dataStore } from '@/utils/indexedDBAdapter';

// 默认商品模板
const defaultTemplates: ProductTemplate[] = [
  {
    id: 'template_1',
    name: '普通纱窗',
    category: '纱窗',
    unitPrice: 120,
    pricingMethod: 'area',
    unit: '平方米',
    minQuantity: 1,
    description: '标准防蚊纱窗，不足1平方米按1平方米计算',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'template_2',
    name: '金刚网纱窗',
    category: '纱窗',
    unitPrice: 180,
    pricingMethod: 'area',
    unit: '平方米',
    minQuantity: 1,
    description: '加强型金刚网纱窗，防盗防蚊',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'template_3',
    name: '纱窗配件',
    category: '五金配件',
    unitPrice: 15,
    pricingMethod: 'unit',
    unit: '件',
    description: '纱窗安装配件',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

function generateTemplateId(): string {
  return `template_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
}

export const useProductStore = create<ProductStore>()(
  persist(
    (set, get) => ({
      templates: defaultTemplates,
      selectedTemplate: null,

      addTemplate: async (templateData) => {
        const newTemplate: ProductTemplate = {
          ...templateData,
          id: generateTemplateId(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 保存到 IndexedDB
        try {
          await dataStore.saveProduct(newTemplate);
        } catch (error) {
          console.error('Failed to save product to IndexedDB:', error);
        }

        set(state => ({
          templates: [...state.templates, newTemplate]
        }));
      },

      updateTemplate: async (id, updates) => {
        const { templates } = get();
        const updatedTemplate = templates.find(t => t.id === id);

        if (updatedTemplate) {
          const finalTemplate = {
            ...updatedTemplate,
            ...updates,
            updatedAt: new Date().toISOString()
          };

          // 更新到 IndexedDB
          try {
            await dataStore.saveProduct(finalTemplate);
          } catch (error) {
            console.error('Failed to update product in IndexedDB:', error);
          }
        }

        set(state => ({
          templates: state.templates.map(template =>
            template.id === id
              ? { ...template, ...updates, updatedAt: new Date().toISOString() }
              : template
          )
        }));
      },

      deleteTemplate: async (id) => {
        // 从 IndexedDB 删除
        try {
          await dataStore.deleteProduct(id);
        } catch (error) {
          console.error('Failed to delete product from IndexedDB:', error);
        }

        set(state => ({
          templates: state.templates.filter(template => template.id !== id),
          selectedTemplate: state.selectedTemplate?.id === id ? null : state.selectedTemplate
        }));
      },

      loadTemplates: async () => {
        try {
          const templates = await dataStore.getAllProducts();
          set({ templates: templates.length > 0 ? templates : defaultTemplates });
        } catch (error) {
          console.error('Failed to load products from IndexedDB:', error);
          set({ templates: defaultTemplates });
        }
      },

      setSelectedTemplate: (template) => {
        set({ selectedTemplate: template });
      },

      getTemplatesByCategory: (category) => {
        const { templates } = get();
        return templates.filter(template => template.category === category);
      },

      calculateProduct: (template, dimensions) => {
        let calculatedQuantity = 1;
        let actualQuantity = 1;

        switch (template.pricingMethod) {
          case 'area':
            if (dimensions?.length && dimensions?.width) {
              calculatedQuantity = (dimensions.length / 1000) * (dimensions.width / 1000); // 转换为平方米
              actualQuantity = Math.max(calculatedQuantity, template.minQuantity || 1);
            }
            break;

          case 'length':
            if (dimensions?.length) {
              calculatedQuantity = dimensions.length / 1000; // 转换为米
              actualQuantity = Math.max(calculatedQuantity, template.minQuantity || 1);
            }
            break;

          case 'weight':
            if (dimensions?.weight) {
              calculatedQuantity = dimensions.weight;
              actualQuantity = Math.max(calculatedQuantity, template.minQuantity || 1);
            }
            break;

          case 'unit':
          default:
            calculatedQuantity = 1;
            actualQuantity = 1;
            break;
        }

        const totalPrice = actualQuantity * template.unitPrice;

        return {
          template,
          dimensions,
          calculatedQuantity,
          actualQuantity,
          unitPrice: template.unitPrice,
          totalPrice
        };
      }
    }),
    {
      name: 'product-store',
      partialize: (state) => ({
        templates: state.templates
      })
    }
  )
);
