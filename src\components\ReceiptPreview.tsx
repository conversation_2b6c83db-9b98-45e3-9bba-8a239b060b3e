'use client';

import { useOrderStore } from '@/store/orderStore';
import { useBusinessStore } from '@/store/businessStore';
import { formatCurrency, formatDate } from '@/utils/orderUtils';

function getPaymentMethodText(method?: string): string {
  const methods: Record<string, string> = {
    cash: '现金',
    card: '银行卡',
    alipay: '支付宝',
    wechat: '微信支付',
    transfer: '银行转账'
  };
  return methods[method || 'cash'] || '现金';
}

export function ReceiptPreview() {
  const { currentOrder } = useOrderStore();
  const { businessInfo } = useBusinessStore();

  if (!currentOrder) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500">
        请填写订单信息以查看预览
      </div>
    );
  }

  const { customer, address, orderInfo, template } = currentOrder;
  const styles = template.styles;

  return (
    <div className="w-full">
      {/* 固定比例容器 */}
      <div
        className="w-full mx-auto bg-white shadow-lg"
        style={{
          aspectRatio: '210 / 297', // A4 比例
          maxWidth: '100%',
          minHeight: '400px'
        }}
      >
        <div
          id="receipt-preview-content"
          className="w-full h-full overflow-auto"
          style={{
            fontFamily: styles.fontFamily,
            fontSize: styles.fontSize,
            color: styles.primaryColor,
            backgroundColor: 'white',
            padding: '20px',
            lineHeight: '1.4',
            transform: 'scale(1)',
            transformOrigin: 'top left'
          }}
        >
          {/* 企业信息头部 */}
          {businessInfo && (
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'flex-start',
              marginBottom: '20px',
              paddingBottom: '15px',
              borderBottom: '1px solid #e5e7eb'
            }}>
              <div style={{ flex: 1 }}>
                {businessInfo.logo && (
                  <img
                    src={businessInfo.logo}
                    alt="企业Logo"
                    style={{
                      height: '40px',
                      marginBottom: '8px',
                      objectFit: 'contain'
                    }}
                  />
                )}
                <h2 style={{
                  margin: '0 0 5px 0',
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: styles.primaryColor
                }}>
                  {businessInfo.companyName}
                </h2>
                <div style={{ fontSize: '11px', color: '#6b7280', lineHeight: '1.3' }}>
                  {businessInfo.phone && <div>电话: {businessInfo.phone}</div>}
                  {businessInfo.wechat && <div>微信: {businessInfo.wechat}</div>}
                  {businessInfo.address && <div>地址: {businessInfo.address}</div>}
                </div>
              </div>

              {businessInfo.qrCode && (
                <div style={{ marginLeft: '15px' }}>
                  <img
                    src={businessInfo.qrCode}
                    alt="企业二维码"
                    style={{
                      width: '60px',
                      height: '60px',
                      objectFit: 'contain'
                    }}
                  />
                </div>
              )}
            </div>
          )}
      {/* 头部 */}
      <div
        style={{
          textAlign: 'center',
          borderBottom: `2px solid ${styles.primaryColor}`,
          paddingBottom: '16px',
          marginBottom: '24px'
        }}
      >
        <h1
          style={{
            margin: '0',
            fontSize: '24px',
            fontWeight: 'bold',
            color: styles.primaryColor
          }}
        >
          订单收据
        </h1>
        <p
          style={{
            margin: '8px 0 0 0',
            fontSize: '14px',
            color: '#666'
          }}
        >
          ORDER RECEIPT
        </p>
      </div>

      {/* 订单信息和客户信息 */}
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '24px',
          marginBottom: '24px'
        }}
      >
        <div>
          <h3
            style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              fontWeight: 'bold',
              color: styles.primaryColor
            }}
          >
            订单信息
          </h3>
          <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
            <p style={{ margin: '4px 0' }}>
              <strong>订单号:</strong> {orderInfo.orderNumber}
            </p>
            <p style={{ margin: '4px 0' }}>
              <strong>日期:</strong> {formatDate(orderInfo.date, 'yyyy年MM月dd日')}
            </p>
            <p style={{ margin: '4px 0' }}>
              <strong>支付方式:</strong> {getPaymentMethodText(orderInfo.paymentMethod)}
            </p>
          </div>
        </div>

        <div>
          <h3
            style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              fontWeight: 'bold',
              color: styles.primaryColor
            }}
          >
            客户信息
          </h3>
          <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
            <p style={{ margin: '4px 0' }}>
              <strong>姓名:</strong> {customer.name}
            </p>
            <p style={{ margin: '4px 0' }}>
              <strong>电话:</strong> {customer.phone}
            </p>
            {customer.email && (
              <p style={{ margin: '4px 0' }}>
                <strong>邮箱:</strong> {customer.email}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* 地址信息 */}
      <div style={{ marginBottom: '24px' }}>
        <h3
          style={{
            margin: '0 0 8px 0',
            fontSize: '14px',
            fontWeight: 'bold',
            color: styles.primaryColor
          }}
        >
          收货地址
        </h3>
        <p
          style={{
            margin: '0',
            padding: '8px',
            backgroundColor: styles.secondaryColor,
            borderRadius: '4px',
            fontSize: '12px'
          }}
        >
          {address.shippingAddress}
        </p>

        {address.billingAddress && (
          <>
            <h3
              style={{
                margin: '12px 0 8px 0',
                fontSize: '14px',
                fontWeight: 'bold',
                color: styles.primaryColor
              }}
            >
              账单地址
            </h3>
            <p
              style={{
                margin: '0',
                padding: '8px',
                backgroundColor: styles.secondaryColor,
                borderRadius: '4px',
                fontSize: '12px'
              }}
            >
              {address.billingAddress}
            </p>
          </>
        )}
      </div>

      {/* 商品列表 */}
      <div style={{ marginBottom: '24px' }}>
        <h3
          style={{
            margin: '0 0 12px 0',
            fontSize: '14px',
            fontWeight: 'bold',
            color: styles.primaryColor
          }}
        >
          商品明细
        </h3>

        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            border: '1px solid #ddd',
            fontSize: '12px'
          }}
        >
          <thead>
            <tr style={{ backgroundColor: styles.primaryColor, color: 'white' }}>
              <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>
                商品名称
              </th>
              <th style={{ padding: '8px', textAlign: 'center', border: '1px solid #ddd' }}>
                数量
              </th>
              <th style={{ padding: '8px', textAlign: 'right', border: '1px solid #ddd' }}>
                单价
              </th>
              <th style={{ padding: '8px', textAlign: 'right', border: '1px solid #ddd' }}>
                小计
              </th>
            </tr>
          </thead>
          <tbody>
            {orderInfo.items.map((item, index) => (
              <tr
                key={item.id}
                style={{
                  backgroundColor: index % 2 === 0 ? 'white' : styles.secondaryColor
                }}
              >
                <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                  {item.name}
                </td>
                <td style={{ padding: '8px', textAlign: 'center', border: '1px solid #ddd' }}>
                  {item.quantity}
                </td>
                <td style={{ padding: '8px', textAlign: 'right', border: '1px solid #ddd' }}>
                  {formatCurrency(item.unitPrice)}
                </td>
                <td style={{ padding: '8px', textAlign: 'right', border: '1px solid #ddd' }}>
                  {formatCurrency(item.totalPrice)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 费用汇总 */}
      <div
        style={{
          borderTop: `2px solid ${styles.primaryColor}`,
          paddingTop: '16px',
          marginBottom: '24px'
        }}
      >
        <div style={{ textAlign: 'right', fontSize: '12px' }}>
          <div style={{ marginBottom: '8px' }}>
            <span style={{ display: 'inline-block', width: '80px' }}>小计:</span>
            <span style={{ fontWeight: 'bold' }}>
              {formatCurrency(orderInfo.subtotal)}
            </span>
          </div>

          {(orderInfo.tax || 0) > 0 && (
            <div style={{ marginBottom: '8px' }}>
              <span style={{ display: 'inline-block', width: '80px' }}>税费:</span>
              <span>{formatCurrency(orderInfo.tax || 0)}</span>
            </div>
          )}

          {(orderInfo.discount || 0) > 0 && (
            <div style={{ marginBottom: '8px' }}>
              <span style={{ display: 'inline-block', width: '80px' }}>折扣:</span>
              <span>-{formatCurrency(orderInfo.discount || 0)}</span>
            </div>
          )}

          <div
            style={{
              fontSize: '16px',
              fontWeight: 'bold',
              color: styles.primaryColor,
              borderTop: '1px solid #ddd',
              paddingTop: '8px',
              marginTop: '8px'
            }}
          >
            <span style={{ display: 'inline-block', width: '80px' }}>总计:</span>
            <span>{formatCurrency(orderInfo.total)}</span>
          </div>
        </div>
      </div>

      {/* 备注 */}
      {orderInfo.notes && (
        <div style={{ marginBottom: '24px' }}>
          <h3
            style={{
              margin: '0 0 8px 0',
              fontSize: '14px',
              fontWeight: 'bold',
              color: styles.primaryColor
            }}
          >
            备注
          </h3>
          <p
            style={{
              margin: '0',
              padding: '8px',
              backgroundColor: styles.secondaryColor,
              borderRadius: '4px',
              fontSize: '12px'
            }}
          >
            {orderInfo.notes}
          </p>
        </div>
      )}

      {/* 页脚 */}
      <div
        style={{
          textAlign: 'center',
          borderTop: '1px solid #ddd',
          paddingTop: '16px',
          color: '#666',
          fontSize: '10px'
        }}
      >
        <p style={{ margin: '0' }}>感谢您的购买！</p>
        <p style={{ margin: '4px 0 0 0' }}>如有问题，请联系客服</p>
      </div>
        </div>
      </div>
    </div>
  );
}
